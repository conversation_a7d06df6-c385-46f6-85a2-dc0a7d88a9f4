✦ This is an excellent observation, and it points to a standard, albeit sometimes confusing, feature of email systems. Here’s the
  breakdown of how this happens:

  The Two "From" Addresses

  Every email has two "from" addresses, which serve different purposes:

   1. The Envelope Sender (`smtp.mailfrom`):
       * This is the address used during the technical SMTP handshake (the MAIL FROM command).
       * It's the "return path" for the email. If the email bounces or fails, the error report goes to this address.
       * It's the address that SPF (Sender Policy Framework) checks are performed against.
       * In your log, <NAME_EMAIL>. This means the <NAME_EMAIL> is the one that
         actually authenticated with the mail server (Microsoft Outlook in this case) to send the email.

   2. The Header Sender (`From:`):
       * This is the address that is part of the email's headers, which is the main body of the message.
       * It's what the recipient sees as the "From" address in their email client (e.g., Gmail, Outlook).
       * It's the address that DKIM (DomainKeys Identified Mail) and DMARC (Domain-based Message Authentication, Reporting, and
         Conformance) use for validation.
       * In your log, <NAME_EMAIL>. Your application is correctly parsing this From: header to identify the
         sender.

  How Did This Happen in Your Case?

  This situation is the result of a "Send As" or "Send on Behalf of" permission.

  Here is the most likely scenario:

   1. <NAME_EMAIL> has been granted permission by their email administrator to "Send As" the user
      <EMAIL>.
   2. The user logged into their email <NAME_EMAIL>.
   3. They composed a new email <NAME_EMAIL> in the "From" field.
   4. When they hit send, the mail server (Microsoft) did two things:
       * It initiated the technical sending process using the authenticated user's address, <EMAIL>, as the
         envelope sender (smtp.mailfrom).
       * It created the email message with the From: header set to what the user chose: <EMAIL>.

  Evidence in Your Logs

  The Authentication-Results header you provided perfectly confirms this:

   * spf=pass ... smtp.mailfrom=<EMAIL>: SPF passed because the sending IP address was authorized for the
     <NAME_EMAIL>.
   * dkim=pass header.i=@motadata.com: DKIM passed because the email was correctly signed for the header sender's domain
     (motadata.com).
   * dmarc=pass ... header.from=motadata.com: DMARC passed because the domains in the SPF and DKIM checks aligned with the domain
     in the From: header.

  Conclusion: Your application is behaving correctly by identifying the sender from the From: header. The logs show a valid
  email sent using "Send As" permissions, where the technical sender (smtp.mailfrom) is different from the display/logical
  sender (From: header).
